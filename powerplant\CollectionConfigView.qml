import QtQuick 2.12
import QtQuick.Controls 2.5
import QtQuick.Layouts 1.12

Page {
    id: configPage

    // 信号定义
    signal navigateBack()

    // 本地状态管理
    property var rs485Config: ({})
    property var dcsRS485Config: ({})
    property var parameterAdjustmentConfig: ({})
    property bool hasUnsavedChanges: false

    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回监控"
                onClicked: {
                    if (hasUnsavedChanges) {
                        confirmDialog.open()
                    } else {
                        stackView.pop()
                    }
                }
            }
            Label {
                text: "采集配置管理"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
                color: "white"
            }

            Button {
                text: "重新加载"
                onClicked: {
                    loadConfigs()
                }
                background: Rectangle {
                    color: parent.pressed ? "#ff9800" : "#ffc107"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Button {
                text: "保存配置"
                enabled: hasUnsavedChanges
                onClicked: {
                    saveConfigs()
                }
                background: Rectangle {
                    color: parent.enabled ? (parent.pressed ? "#388e3c" : "#4caf50") : "#666666"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }

    // 确认对话框
    Dialog {
        id: confirmDialog
        anchors.centerIn: parent
        width: 400
        height: 200
        title: "未保存的更改"

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            Label {
                text: "您有未保存的配置更改，是否要保存？"
                font.pixelSize: 16
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }

            RowLayout {
                Layout.fillWidth: true

                Button {
                    text: "保存并返回"
                    Layout.fillWidth: true
                    onClicked: {
                        saveConfigs()
                        confirmDialog.close()
                        stackView.pop()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#388e3c" : "#4caf50"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                Button {
                    text: "不保存"
                    Layout.fillWidth: true
                    onClicked: {
                        confirmDialog.close()
                        stackView.pop()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#d32f2f" : "#f44336"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                Button {
                    text: "取消"
                    Layout.fillWidth: true
                    onClicked: {
                        confirmDialog.close()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#757575" : "#9e9e9e"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }

    // 重启数据采集确认对话框
    Dialog {
        id: restartDialog
        anchors.centerIn: parent
        width: 500
        height: 250
        title: "配置保存成功"

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            Label {
                text: "✅ 配置已成功保存到config.ini文件！"
                font.pixelSize: 16
                font.bold: true
                color: "#4caf50"
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }

            Label {
                text: "为了使新的配置生效，需要重启应用程序。\n这将会：\n• 保存当前配置到文件\n• 使用重启脚本彻底关闭当前应用程序\n• 自动启动新的应用程序实例\n• 使用新配置重新初始化所有模块\n• 重新建立设备连接和数据采集\n\n是否现在重启应用程序？"
                font.pixelSize: 14
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
                color: "#333333"
            }

            RowLayout {
                Layout.fillWidth: true

                Button {
                    text: "立即重启应用"
                    Layout.fillWidth: true
                    onClicked: {
                        restartDialog.close()
                        restartApplication()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#388e3c" : "#4caf50"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        font.bold: true
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                Button {
                    text: "稍后手动重启"
                    Layout.fillWidth: true
                    onClicked: {
                        restartDialog.close()
                        // 显示提示消息
                        successMessage.text = "配置已保存！请手动重启应用程序使配置生效。"
                        successMessage.visible = true
                        successTimer.start()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#757575" : "#9e9e9e"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }

    // 主要内容区域
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        // RS485配置区域（顶部，保持不变）
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: rs485Column.implicitHeight + 40
            color: "#ffffff"
            radius: 12
            border.color: "#e0e0e0"
            border.width: 1

            ColumnLayout {
                id: rs485Column
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15

                Label {
                    text: "RS485 烟气分析仪配置"
                    font.pixelSize: 18
                    font.bold: true
                    color: "#333333"
                }

                GridLayout {
                    Layout.fillWidth: true
                    columns: 12
                    columnSpacing: 15
                    rowSpacing: 15

                    // 串口号
                    Label {
                        text: "串口号:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485PortCombo
                        Layout.preferredWidth: 100
                        editable: true
                        model: ["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "COM10"]
                        currentIndex: {
                            var value = rs485Config.Port || "COM5"
                            var index = model.indexOf(value)
                            return index >= 0 ? index : -1
                        }
                        editText: rs485Config.Port || "COM5"
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.Port || "COM5")) {
                                hasUnsavedChanges = true
                            }
                        }
                        onEditTextChanged: {
                            if (editText !== (rs485Config.Port || "COM5")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 波特率
                    Label {
                        text: "波特率:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485BaudRateCombo
                        Layout.preferredWidth: 100
                        model: ["1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"]
                        currentIndex: {
                            var value = rs485Config.BaudRate || "9600"
                            return model.indexOf(value)
                        }
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.BaudRate || "9600")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 停止位
                    Label {
                        text: "停止位:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485StopBitsCombo
                        Layout.preferredWidth: 100
                        model: ["1", "2"]
                        currentIndex: {
                            var value = rs485Config.StopBits || "1"
                            return model.indexOf(value)
                        }
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.StopBits || "1")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 校验位
                    Label {
                        text: "校验位:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485ParityCombo
                        Layout.preferredWidth: 100
                        model: ["N", "E", "O"]
                        currentIndex: {
                            var value = rs485Config.Parity || "N"
                            return model.indexOf(value)
                        }
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.Parity || "N")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 数据位
                    Label {
                        text: "数据位:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485DataBitsCombo
                        Layout.preferredWidth: 100
                        model: ["7", "8"]
                        currentIndex: {
                            var value = rs485Config.DataBits || "8"
                            return model.indexOf(value)
                        }
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.DataBits || "8")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 超时时间
                    Label {
                        text: "超时时间(s):"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485TimeoutField
                        Layout.preferredWidth: 100
                        text: rs485Config.Timeout || ""
                        validator: DoubleValidator { bottom: 0.1; top: 10.0; decimals: 1 }
                        onTextChanged: {
                            if (text !== (rs485Config.Timeout || "")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 数据采集间隔
                    Label {
                        text: "数据采集间隔(s):"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485CollectionIntervalField
                        Layout.preferredWidth: 100
                        text: rs485Config.CollectionInterval || "15"
                        validator: IntValidator { bottom: 1; top: 300 }
                        onTextChanged: {
                            if (text !== (rs485Config.CollectionInterval || "15")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }



                    // 抽气泵电流传感器设备地址
                    Label {
                        text: "抽气泵电流地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485CurrentField
                        Layout.preferredWidth: 100
                        text: rs485Config.Current || "60"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Current || "60")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }



                    // 压力表传感器设备地址
                    Label {
                        text: "压力表设备地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485VoltageField
                        Layout.preferredWidth: 100
                        text: rs485Config.Voltage || "50"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Voltage || "50")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // O2传感器设备地址
                    Label {
                        text: "O2设备地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485O2Field
                        Layout.preferredWidth: 100
                        text: rs485Config.O2 || "4"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.O2 || "4")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // CO传感器设备地址
                    Label {
                        text: "CO设备地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485CoField
                        Layout.preferredWidth: 100
                        text: rs485Config.Co || "1"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Co || "1")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 冷凝器温度传感器设备地址
                    Label {
                        text: "冷凝器温度地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485TempatureField
                        Layout.preferredWidth: 100
                        text: rs485Config.Tempature || "40"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Tempature || "40")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 开关量信号1设备地址
                    Label {
                        text: "开关信号1地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485Switch1Field
                        Layout.preferredWidth: 100
                        text: rs485Config.Switch1 || "80"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Switch1 || "80")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 反吹反馈停止后的延迟恢复时间
                    Label {
                        text: "反吹延迟时间(s):"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485BackflowDelayTimeField
                        Layout.preferredWidth: 100
                        text: rs485Config.BackflowDelayTime || "60"
                        validator: IntValidator { bottom: 1; top: 300 }
                        onTextChanged: {
                            if (text !== (rs485Config.BackflowDelayTime || "60")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }
                }
            }
        }

        // 下方左右分栏区域
        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 20

            // 左侧：DCS RS485配置区域（带独立滚动条）
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: "#ffffff"
                radius: 12
                border.color: "#e0e0e0"
                border.width: 1

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 15

                    Label {
                        text: "DCS RS485 采集配置"
                        font.pixelSize: 18
                        font.bold: true
                        color: "#195399"
                    }

                    ScrollView {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true

                        ColumnLayout {
                            width: parent.width
                            spacing: 20

                            // DCS RS485 连接配置
                            GroupBox {
                                Layout.fillWidth: true
                                title: "DCS RS485 连接配置"
                                font.pixelSize: 16
                                font.bold: true

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 15

                                    // 串口号
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "串口号:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 120
                                        }
                                        ComboBox {
                                            id: dcsPortCombo
                                            Layout.preferredWidth: 120
                                            editable: true
                                            model: ["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "COM10"]
                                            currentIndex: {
                                                var value = dcsRS485Config.Port || "COM6"
                                                var index = model.indexOf(value)
                                                return index >= 0 ? index : -1
                                            }
                                            editText: dcsRS485Config.Port || "COM6"
                                            onCurrentTextChanged: {
                                                if (currentText !== (dcsRS485Config.Port || "COM6")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                            onEditTextChanged: {
                                                if (editText !== (dcsRS485Config.Port || "COM6")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }

                                        // 波特率
                                        Label {
                                            text: "波特率:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 80
                                        }
                                        ComboBox {
                                            id: dcsBaudRateCombo
                                            Layout.preferredWidth: 120
                                            model: ["1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"]
                                            currentIndex: {
                                                var value = dcsRS485Config.BaudRate || "9600"
                                                return model.indexOf(value)
                                            }
                                            onCurrentTextChanged: {
                                                if (currentText !== (dcsRS485Config.BaudRate || "9600")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }

                                        // 停止位
                                        Label {
                                            text: "停止位:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 80
                                        }
                                        ComboBox {
                                            id: dcsStopBitsCombo
                                            Layout.preferredWidth: 80
                                            model: ["1", "2"]
                                            currentIndex: {
                                                var value = dcsRS485Config.StopBits || "1"
                                                return model.indexOf(value)
                                            }
                                            onCurrentTextChanged: {
                                                if (currentText !== (dcsRS485Config.StopBits || "1")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        // 校验位
                                        Label {
                                            text: "校验位:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 120
                                        }
                                        ComboBox {
                                            id: dcsParityCombo
                                            Layout.preferredWidth: 120
                                            model: ["N", "E", "O"]
                                            currentIndex: {
                                                var value = dcsRS485Config.Parity || "N"
                                                return model.indexOf(value)
                                            }
                                            onCurrentTextChanged: {
                                                if (currentText !== (dcsRS485Config.Parity || "N")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }

                                        // 数据位
                                        Label {
                                            text: "数据位:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 80
                                        }
                                        ComboBox {
                                            id: dcsDataBitsCombo
                                            Layout.preferredWidth: 120
                                            model: ["7", "8"]
                                            currentIndex: {
                                                var value = dcsRS485Config.DataBits || "8"
                                                return model.indexOf(value)
                                            }
                                            onCurrentTextChanged: {
                                                if (currentText !== (dcsRS485Config.DataBits || "8")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }

                                        // 超时时间
                                        Label {
                                            text: "超时时间(s):"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 80
                                        }
                                        TextField {
                                            id: dcsTimeoutField
                                            Layout.preferredWidth: 120
                                            text: dcsRS485Config.Timeout || "2.0"
                                            validator: DoubleValidator { bottom: 0.1; top: 10.0; decimals: 1 }
                                            onTextChanged: {
                                                if (text !== (dcsRS485Config.Timeout || "2.0")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // DCS RS485 设备配置
                            GroupBox {
                                Layout.fillWidth: true
                                title: "DCS RS485 设备配置"
                                font.pixelSize: 16
                                font.bold: true

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 15

                                    // 数据采集间隔
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "数据采集间隔(s):"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 150
                                        }
                                        TextField {
                                            id: dcsCollectionIntervalField
                                            Layout.preferredWidth: 120
                                            text: dcsRS485Config.CollectionInterval || "10"
                                            validator: IntValidator { bottom: 1; top: 300 }
                                            onTextChanged: {
                                                if (text !== (dcsRS485Config.CollectionInterval || "10")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }

                                        Label {
                                            text: "设备地址:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 100
                                        }
                                        TextField {
                                            id: dcsDeviceAddressField
                                            Layout.preferredWidth: 120
                                            text: dcsRS485Config.DeviceAddress || "12"
                                            validator: IntValidator { bottom: 1; top: 255 }
                                            onTextChanged: {
                                                if (text !== (dcsRS485Config.DeviceAddress || "12")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 寄存器配置
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "起始寄存器:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 150
                                        }
                                        TextField {
                                            id: dcsStartRegisterField
                                            Layout.preferredWidth: 120
                                            text: dcsRS485Config.StartRegister || "31"
                                            validator: IntValidator { bottom: 0; top: 65535 }
                                            onTextChanged: {
                                                if (text !== (dcsRS485Config.StartRegister || "31")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }

                                        Label {
                                            text: "寄存器数量:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 100
                                        }
                                        TextField {
                                            id: dcsRegisterCountField
                                            Layout.preferredWidth: 120
                                            text: dcsRS485Config.RegisterCount || "44"
                                            validator: IntValidator { bottom: 1; top: 125 }
                                            onTextChanged: {
                                                if (text !== (dcsRS485Config.RegisterCount || "44")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 关联锅炉
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "关联锅炉:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 150
                                        }
                                        TextField {
                                            id: dcsAssociatedBoilerField
                                            Layout.fillWidth: true
                                            text: dcsRS485Config.AssociatedBoiler || "#3机组"
                                            onTextChanged: {
                                                if (text !== (dcsRS485Config.AssociatedBoiler || "#3机组")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 数据偏移量配置说明
                                    Label {
                                        text: "数据映射配置 - 基于88字节数据的字节偏移量\n报文格式：7字节头部 + 88字节数据，数据从第8字节开始"
                                        font.pixelSize: 12
                                        color: "#888888"
                                        Layout.fillWidth: true
                                        wrapMode: Text.WordWrap
                                    }
                                }
                            }




                        }
                    }
                }
            }

            // 右侧：写入配置区域（暂时保留空的）
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: "#ffffff"
                radius: 12
                border.color: "#e0e0e0"
                border.width: 1

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 15

                    Label {
                        text: "写入配置区域"
                        font.pixelSize: 18
                        font.bold: true
                        color: "#195399"
                    }

                    Label {
                        text: "此区域暂时保留空的，后续可添加写入配置功能"
                        font.pixelSize: 14
                        color: "#666666"
                        Layout.fillWidth: true
                        wrapMode: Text.WordWrap
                    }

                    Item {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                    }


                }
            }

        }
    }







    }








        }
    }

    // 成功消息
    Rectangle {
        id: successMessage
        anchors.top: parent.top
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.topMargin: 20
        width: 300
        height: 50
        color: "#4caf50"
        radius: 8
        visible: false
        z: 1000

        property string text: ""

        Label {
            id: successLabel
            anchors.centerIn: parent
            text: successMessage.text
            color: "white"
            font.pixelSize: 16
            font.bold: true
        }
    }

    // 错误消息
    Rectangle {
        id: errorMessage
        anchors.top: parent.top
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.topMargin: 20
        width: 300
        height: 50
        color: "#f44336"
        radius: 8
        visible: false
        z: 1000

        property string text: ""

        Label {
            id: errorLabel
            anchors.centerIn: parent
            text: errorMessage.text
            color: "white"
            font.pixelSize: 16
            font.bold: true
        }
    }

    // 成功消息定时器
    Timer {
        id: successTimer
        interval: 3000
        onTriggered: successMessage.visible = false
    }

    // 错误消息定时器
    Timer {
        id: errorTimer
        interval: 3000
        onTriggered: errorMessage.visible = false
    }

    // 页面初始化时加载配置
    Component.onCompleted: {
        loadConfigs()
    }

    // 加载配置的函数
    function loadConfigs() {
        if (typeof configManagerQML !== 'undefined') {
            rs485Config = configManagerQML.getRS485Config()
            dcsRS485Config = configManagerQML.getDCSRS485Config()
            parameterAdjustmentConfig = {}  // 暂时为空
            hasUnsavedChanges = false
        }
    }

    // 保存配置的函数
    function saveConfigs() {
        if (typeof configManagerQML !== 'undefined') {
            // 收集RS485通信配置（只包含串口通信参数）
            var newRS485Config = {
                "Port": rs485PortCombo.editable ? rs485PortCombo.editText : rs485PortCombo.currentText,
                "BaudRate": rs485BaudRateCombo.currentText,
                "StopBits": rs485StopBitsCombo.currentText,
                "Parity": rs485ParityCombo.currentText,
                "DataBits": rs485DataBitsCombo.currentText,
                "Timeout": rs485TimeoutField.text
            }

            // 收集烟气分析仪设备配置（设备地址和采集参数）
            var newSmokeAnalyzerConfig = {
                "CollectionInterval": rs485CollectionIntervalField.text,
                "Current": rs485CurrentField.text,
                "Voltage": rs485VoltageField.text,
                "O2": rs485O2Field.text,
                "Co": rs485CoField.text,
                "Tempature": rs485TempatureField.text,
                "Switch1": rs485Switch1Field.text,
                "BackflowDelayTime": rs485BackflowDelayTimeField.text
            }

            // 收集DCS RS485配置
            var newDCSRS485Config = {
                "Port": dcsPortCombo.editable ? dcsPortCombo.editText : dcsPortCombo.currentText,
                "BaudRate": dcsBaudRateCombo.currentText,
                "StopBits": dcsStopBitsCombo.currentText,
                "Parity": dcsParityCombo.currentText,
                "DataBits": dcsDataBitsCombo.currentText,
                "Timeout": dcsTimeoutField.text
            }

            // 收集DCS设备配置
            var newDCSDeviceConfig = {
                "CollectionInterval": dcsCollectionIntervalField.text,
                "DeviceAddress": dcsDeviceAddressField.text,
                "StartRegister": dcsStartRegisterField.text,
                "RegisterCount": dcsRegisterCountField.text,
                "AssociatedBoiler": dcsAssociatedBoilerField.text
            }

            // 收集参数调整配置
            var newParameterAdjustmentConfig = {
                "AutoAdjustmentEnabled": autoAdjustmentEnabledSwitch.checked ? "true" : "false",
                "AdjustmentCheckInterval": adjustmentCheckIntervalField.text,
                "RawMaterialDiffThreshold": rawMaterialDiffThresholdField.text,
                "RawMaterialAdjustmentStep": rawMaterialAdjustmentStepField.text,
                "FurnaceTempDiffThreshold": furnaceTempDiffThresholdField.text,
                "FurnaceTempMaxValue": furnaceTempMaxValueField.text,
                "FurnaceTempMinValue": furnaceTempMinValueField.text,
                "CoalFeedAdjustmentStep": coalFeedAdjustmentStepField.text,
                "CoalFeedMaxValue": coalFeedMaxValueField.text,
                "CoalFeedMinValue": coalFeedMinValueField.text,
                "OxygenConcentrationDiffThreshold": oxygenConcentrationDiffThresholdField.text,
                "OxygenConcentrationSetpoint": oxygenConcentrationSetpointField.text,
                "OxygenRawMaterialAdjustmentStep": oxygenRawMaterialAdjustmentStepField.text,
                "OxygenCoalFeedAdjustmentStep": oxygenCoalFeedAdjustmentStepField.text,
                "FurnacePressureDiffThreshold": furnacePressureDiffThresholdField.text,
                "FurnacePressureSetpoint": furnacePressureSetpointField.text,
                "FurnacePressureSetpointMin": furnacePressureSetpointMinField.text,
                "FurnacePressureSetpointMax": furnacePressureSetpointMaxField.text,
                "InducedDraftFanAdjustmentStep": inducedDraftFanAdjustmentStepField.text,
                "InducedDraftFanMaxSpeed": inducedDraftFanMaxSpeedField.text,
                "InducedDraftFanMinSpeed": inducedDraftFanMinSpeedField.text
            }

            // 先设置所有配置到内存中，然后一次性保存
            // 设置RS485通信配置
            for (var key in newRS485Config) {
                configManagerQML.setConfigValue("RS485", key, newRS485Config[key])
            }

            // 获取烟气分析仪设备名称并设置设备配置
            var smokeAnalyzerDeviceName = configManagerQML.getSmokeAnalyzerDeviceName()
            if (smokeAnalyzerDeviceName && smokeAnalyzerDeviceName.length > 0) {
                for (var key in newSmokeAnalyzerConfig) {
                    configManagerQML.setConfigValue(smokeAnalyzerDeviceName, key, newSmokeAnalyzerConfig[key])
                }
            } else {
                for (var key in newSmokeAnalyzerConfig) {
                    configManagerQML.setConfigValue("分解炉1号", key, newSmokeAnalyzerConfig[key])
                }
            }

            // 设置DCS配置（包含基本配置和OPC配置）
            for (var key in newDCSOPCConfig) {
                configManagerQML.setConfigValue("DCS1", key, newDCSOPCConfig[key])
            }

            // 设置参数调整配置
            for (var key in newParameterAdjustmentConfig) {
                configManagerQML.setConfigValue("ParameterAdjustment", key, newParameterAdjustmentConfig[key])
            }

            // 一次性保存所有配置
            var saveSuccess = configManagerQML.saveAllConfigs()

            if (saveSuccess) {
                hasUnsavedChanges = false
                // 合并RS485通信配置和烟气分析仪设备配置
                var combinedRS485Config = {}
                for (var key in newRS485Config) {
                    combinedRS485Config[key] = newRS485Config[key]
                }
                for (var key in newSmokeAnalyzerConfig) {
                    combinedRS485Config[key] = newSmokeAnalyzerConfig[key]
                }
                rs485Config = combinedRS485Config
                dcsOPCConfig = newDCSOPCConfig
                parameterAdjustmentConfig = newParameterAdjustmentConfig

                // 显示配置保存成功，询问是否重启数据采集
                restartDialog.open()
            } else {
                // 显示错误消息
                errorMessage.text = "配置保存失败，请检查输入参数！"
                errorMessage.visible = true
                errorTimer.start()
            }
        }
    }

    // 重启应用程序的函数
    function restartApplication() {
        if (typeof configManagerQML !== 'undefined') {
            // 显示重启进度消息
            successMessage.text = "正在使用重启脚本重启应用程序，请稍候..."
            successMessage.visible = true

            // 调用重启功能
            var success = configManagerQML.restartApplication()

            if (success) {
                successMessage.text = "✅ 重启脚本已启动，应用程序将彻底重启，新的配置将在重启后生效。"
                successMessage.visible = true
                successTimer.start()
            } else {
                errorMessage.text = "❌ 重启脚本启动失败！请手动运行'重启软件.bat'或重启软件。"
                errorMessage.visible = true
                errorTimer.start()
            }
        }
    }

}
